package com.youlai.boot.modules.appointment.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.common.enums.AppoinitMentEnum;
import com.youlai.boot.common.enums.HandleEnum;
import com.youlai.boot.common.exception.BusinessException;
import com.youlai.boot.common.util.UserInfoUtils;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.modules.appointment.converter.AppointmentConverter;
import com.youlai.boot.modules.appointment.mapper.AppointmentMapper;
import com.youlai.boot.modules.appointment.model.dto.AppointmentDTO;
import com.youlai.boot.modules.appointment.model.entity.Appointment;
import com.youlai.boot.modules.appointment.model.form.AppointmentForm;
import com.youlai.boot.modules.appointment.model.query.AppointmentQuery;
import com.youlai.boot.modules.appointment.model.query.MyAppointmentQuery;
import com.youlai.boot.modules.appointment.model.vo.AppointmentVO;
import com.youlai.boot.modules.appointment.service.AppointmentService;
import com.youlai.boot.modules.opinion.model.entity.Opinion;
import com.youlai.boot.system.mapper.UserMapper;
import com.youlai.boot.system.model.bo.UserBO;
import com.youlai.boot.system.model.entity.User;
import com.youlai.boot.system.model.vo.UserProfileVO;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 约见管理服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
@RequiredArgsConstructor
public class AppointmentServiceImpl extends ServiceImpl<AppointmentMapper, Appointment> implements AppointmentService {

    private final AppointmentConverter appointmentConverter;
    private final UserMapper userMapper;

    @Override
    public IPage<AppointmentVO> getAppointmentPage(AppointmentQuery queryParams) {
        // 查询参数
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        String appointmentName = queryParams.getAppointmentName();
        String appointmentUnit = queryParams.getAppointmentUnit();
        String appointmentDepartment = queryParams.getAppointmentDepartment();
        HandleEnum handleStatus = queryParams.getHandleStatus();

        // 构建分页对象
        Page<Appointment> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<Appointment> queryWrapper = new LambdaQueryWrapper<Appointment>()
                .like(StrUtil.isNotBlank(appointmentName), Appointment::getAppointmentName, appointmentName)
                .like(StrUtil.isNotBlank(appointmentUnit), Appointment::getAppointmentUnit, appointmentUnit)
                .like(StrUtil.isNotBlank(appointmentDepartment), Appointment::getAppointmentDepartment, appointmentDepartment)
                .eq(handleStatus != null, Appointment::getHandleStatus, handleStatus)
                .ge(StrUtil.isNotBlank(queryParams.getStartTimeBegin()), Appointment::getAppointmentStartTime, queryParams.getStartTimeBegin())
                .le(StrUtil.isNotBlank(queryParams.getStartTimeEnd()), Appointment::getAppointmentStartTime, queryParams.getStartTimeEnd())
                .orderByDesc(Appointment::getCreateTime);

        // 根据约见状态进行过滤
        AppoinitMentEnum appointmentStatus = queryParams.getAppointmentStatus();
        if (appointmentStatus != null) {
            LocalDateTime now = LocalDateTime.now();

            if (appointmentStatus == AppoinitMentEnum.WAIT) {
                // 待处理：处理状态为空
                queryWrapper.isNull(Appointment::getHandleStatus);
            } else if (appointmentStatus == AppoinitMentEnum.WAIT_START) {
                // 待开始：处理状态为通过，当前时间在开始时间之前
                queryWrapper.eq(Appointment::getHandleStatus, HandleEnum.PASS)
                          .gt(Appointment::getAppointmentStartTime, now);
            } else if (appointmentStatus == AppoinitMentEnum.MEETING) {
                // 进行中：处理状态为通过，当前时间在开始时间之后且在结束时间之前
                queryWrapper.eq(Appointment::getHandleStatus, HandleEnum.PASS)
                          .le(Appointment::getAppointmentStartTime, now)
                          .ge(Appointment::getAppointmentEndTime, now);
            } else if (appointmentStatus == AppoinitMentEnum.END) {
                // 已结束：处理状态为通过，当前时间在结束时间之后
                queryWrapper.eq(Appointment::getHandleStatus, HandleEnum.PASS)
                          .lt(Appointment::getAppointmentEndTime, now);
            } else if (appointmentStatus == AppoinitMentEnum.REJECT) {
                // 已拒绝：处理状态为拒绝
                queryWrapper.eq(Appointment::getHandleStatus, HandleEnum.REJECT);
            }
        }

        // 查询数据
        Page<Appointment> appointmentPage = this.page(page, queryWrapper);
        // 根据MemberId查询用户信息并设置MemberName和Department
        setMemberInfo(appointmentPage.getRecords());
        // 手动转换实体为VO
        Page<AppointmentVO> voPage = new Page<>();
        voPage.setCurrent(appointmentPage.getCurrent());
        voPage.setSize(appointmentPage.getSize());
        voPage.setTotal(appointmentPage.getTotal());

        List<AppointmentVO> voList = appointmentPage.getRecords().stream()
                .map(entity -> {
                    AppointmentVO vo = new AppointmentVO();
                    vo.setId(entity.getId());
                    vo.setAppointmentName(entity.getAppointmentName());
                    vo.setAppointmentUnit(entity.getAppointmentUnit());
                    vo.setAppointmentContact(entity.getAppointmentContact());
                    vo.setAppointmentDepartment(entity.getAppointmentDepartment());
                    vo.setAppointmentReason(entity.getAppointmentReason());
                    vo.setAppointmentStartTime(entity.getAppointmentStartTime());
                    vo.setAppointmentEndTime(entity.getAppointmentEndTime());
                    vo.setHandleStatus(entity.getHandleStatus());
                    vo.setHandleComment(entity.getHandleComment());
                    vo.setFeedback(entity.getFeedback());
                    vo.setCreateTime(entity.getCreateTime());

                    // 根据处理状态和时间条件设置约见状态
                    if (entity.getHandleStatus() == null) {
                        vo.setAppointmentStatus(AppoinitMentEnum.WAIT);
                    } else if (entity.getHandleStatus().equals(HandleEnum.PASS)) {
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isBefore(entity.getAppointmentStartTime())) {
                            vo.setAppointmentStatus(AppoinitMentEnum.WAIT_START);
                        } else if (now.isAfter(entity.getAppointmentStartTime()) && now.isBefore(entity.getAppointmentEndTime())) {
                            vo.setAppointmentStatus(AppoinitMentEnum.MEETING);
                        } else if (entity.getAppointmentEndTime().isBefore(now)) {
                            vo.setAppointmentStatus(AppoinitMentEnum.END);
                        }
                    } else if (entity.getHandleStatus().equals(HandleEnum.REJECT)) {
                        vo.setAppointmentStatus(AppoinitMentEnum.REJECT);
                    }

                    return vo;
                })
                .collect(Collectors.toList());

        converterToList(voList);

        voPage.setRecords(voList);


        return voPage;

    }

    @Override
    public AppointmentForm getAppointmentFormData(Long id) {
        Appointment entity = this.getById(id);
        Assert.notNull(entity, "约见申请不存在");

        AppointmentForm form = appointmentConverter.entity2Form(entity);
        if (form.getHandleStatus() == null) {
            form.setAppointmentStatus(AppoinitMentEnum.WAIT);
        }

        if (form.getHandleStatus() != null && form.getHandleStatus().equals(HandleEnum.PASS) && LocalDateTime.now().isBefore(form.getAppointmentStartTime())) {
            form.setAppointmentStatus(AppoinitMentEnum.WAIT_START);
        }

        if (form.getHandleStatus() != null && form.getHandleStatus().equals(HandleEnum.PASS) && LocalDateTime.now().isAfter(form.getAppointmentStartTime())
                && LocalDateTime.now().isBefore(form.getAppointmentEndTime())) {
            form.setAppointmentStatus(AppoinitMentEnum.MEETING);
        }

        if (form.getHandleStatus() != null && form.getHandleStatus().equals(HandleEnum.PASS) && form.getAppointmentEndTime().isBefore(LocalDateTime.now())) {
            form.setAppointmentStatus(AppoinitMentEnum.END);
        }

        if (form.getHandleStatus() != null && form.getHandleStatus().equals(HandleEnum.REJECT)) {
            form.setAppointmentStatus(AppoinitMentEnum.REJECT);
        }
// 根据MemberId查询用户信息
        if (form.getMemberId() != null) {
            User user = userMapper.selectById(form.getMemberId());
            if (user != null) {
                form.setAppointmentName(user.getNickname());
                form.setAppointmentUnit(user.getCompany());
            }
        }

        return form;


    }

    @Override
    public boolean saveAppointment(AppointmentForm formData) {
        Appointment entity = appointmentConverter.form2Entity(formData);

        UserProfileVO userInfo = UserInfoUtils.getUserInfo();

        entity.setAppointmentName(userInfo.getNickname());
        entity.setAppointmentUnit(userInfo.getDeptName());
        entity.setMemberId(userInfo.getId());
        //entity.setAppointmentContact(userInfo.getMobile());

        // 设置创建人ID
        entity.setCreateBy(SecurityUtils.getUserId());

        return this.save(entity);
    }

    @Override
    public IPage<AppointmentVO> getMyAppointmentPage(MyAppointmentQuery queryParams) {
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();

        // 构建分页对象
        Page<Appointment> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<Appointment> queryWrapper = new LambdaQueryWrapper<Appointment>()
                .eq(Appointment::getCreateBy, userId)
                .ge(StrUtil.isNotBlank(queryParams.getStartTime()), Appointment::getCreateTime, queryParams.getStartTime())
                .le(StrUtil.isNotBlank(queryParams.getEndTime()), Appointment::getCreateTime, queryParams.getEndTime())
                .like(StrUtil.isNotBlank(queryParams.getKeyword()), Appointment::getAppointmentDepartment, queryParams.getKeyword());

        // 查询数据
        Page<Appointment> appointmentPage = this.page(page, queryWrapper);

        // 实体转换
        return appointmentConverter.entity2Page(appointmentPage);
    }


    @Override
    public boolean updateAppointment(Long id, AppointmentForm formData) {
        Appointment entity = this.getById(id);
        Assert.notNull(entity, "约见申请不存在");

        // 表单数据转换为实体
        Appointment appointment = appointmentConverter.form2Entity(formData);
        appointment.setId(id);
        appointment.setUpdateBy(SecurityUtils.getUserId());
        appointment.setUpdateTime(LocalDateTime.now());

        return this.updateById(appointment);
    }

    @Override
    public boolean deleteAppointments(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的约见管理ID不能为空");

        // 逻辑删除
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());

        return this.removeByIds(idList);
    }

    @Override
    public boolean handleAppointment(Long id, AppointmentDTO dto) {
        Appointment entity = this.getById(id);
        Assert.notNull(entity, "约见申请不存在");

        if (HandleEnum.REJECT.equals(dto.getHandleStatus()) && StrUtil.isBlank(dto.getHandleComment())) {
            throw new BusinessException("处理意见不能为空");
        }

        // 设置处理状态和处理意见
        entity.setHandleStatus(dto.getHandleStatus());
        entity.setHandleComment(dto.getHandleComment());
        entity.setUpdateBy(SecurityUtils.getUserId());
        entity.setUpdateTime(LocalDateTime.now());

        return this.updateById(entity);
    }

    @Override
    public boolean submitFeedback(Long id, String feedback) {
        Appointment entity = this.getById(id);
        Assert.notNull(entity, "约见申请不存在");

        // 设置反馈内容
        entity.setFeedback(feedback);
        entity.setUpdateBy(SecurityUtils.getUserId());
        entity.setUpdateTime(LocalDateTime.now());

        return this.updateById(entity);
    }

    /**
     * 获取约见管理导出数据列表
     *
     * @param queryParams 查询参数
     * @return 数据列表
     */
    @Override
    public List<AppointmentVO> getExportList(AppointmentQuery queryParams) {
        List<Appointment> appointmentList;

        // 如果提供了ID参数，则根据ID查询
        if (StrUtil.isNotBlank(queryParams.getIds())) {
            // 将逗号分隔的ID字符串转换为集合
            List<Long> idList = Arrays.stream(queryParams.getIds().split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            // 根据ID集合查询
            appointmentList = this.listByIds(idList);
        } else {
            // 检查是否有其他查询条件
            boolean hasConditions = StrUtil.isNotBlank(queryParams.getAppointmentName()) ||
                    StrUtil.isNotBlank(queryParams.getAppointmentUnit()) ||
                    StrUtil.isNotBlank(queryParams.getAppointmentDepartment()) ||
                    queryParams.getHandleStatus() != null ||
                    queryParams.getAppointmentStatus() != null ||
                    StrUtil.isNotBlank(queryParams.getStartTimeBegin()) ||
                    StrUtil.isNotBlank(queryParams.getStartTimeEnd());

            if (hasConditions) {
                // 有查询条件，根据条件查询
                LambdaQueryWrapper<Appointment> wrapper = new LambdaQueryWrapper<Appointment>()
                        .like(StrUtil.isNotBlank(queryParams.getAppointmentName()), Appointment::getAppointmentName, queryParams.getAppointmentName())
                        .like(StrUtil.isNotBlank(queryParams.getAppointmentUnit()), Appointment::getAppointmentUnit, queryParams.getAppointmentUnit())
                        .like(StrUtil.isNotBlank(queryParams.getAppointmentDepartment()), Appointment::getAppointmentDepartment, queryParams.getAppointmentDepartment())
                        .eq(queryParams.getHandleStatus() != null, Appointment::getHandleStatus, queryParams.getHandleStatus())
                        .ge(StrUtil.isNotBlank(queryParams.getStartTimeBegin()), Appointment::getAppointmentStartTime, queryParams.getStartTimeBegin())
                        .le(StrUtil.isNotBlank(queryParams.getStartTimeEnd()), Appointment::getAppointmentStartTime, queryParams.getStartTimeEnd())
                        .orderByDesc(Appointment::getCreateTime);

                // 根据约见状态进行过滤
                AppoinitMentEnum appointmentStatus = queryParams.getAppointmentStatus();
                if (appointmentStatus != null) {
                    LocalDateTime now = LocalDateTime.now();

                    if (appointmentStatus == AppoinitMentEnum.WAIT) {
                        // 待处理：处理状态为空
                        wrapper.isNull(Appointment::getHandleStatus);
                    } else if (appointmentStatus == AppoinitMentEnum.WAIT_START) {
                        // 待开始：处理状态为通过，当前时间在开始时间之前
                        wrapper.eq(Appointment::getHandleStatus, HandleEnum.PASS)
                              .gt(Appointment::getAppointmentStartTime, now);
                    } else if (appointmentStatus == AppoinitMentEnum.MEETING) {
                        // 进行中：处理状态为通过，当前时间在开始时间之后且在结束时间之前
                        wrapper.eq(Appointment::getHandleStatus, HandleEnum.PASS)
                              .le(Appointment::getAppointmentStartTime, now)
                              .ge(Appointment::getAppointmentEndTime, now);
                    } else if (appointmentStatus == AppoinitMentEnum.END) {
                        // 已结束：处理状态为通过，当前时间在结束时间之后
                        wrapper.eq(Appointment::getHandleStatus, HandleEnum.PASS)
                              .lt(Appointment::getAppointmentEndTime, now);
                    } else if (appointmentStatus == AppoinitMentEnum.REJECT) {
                        // 已拒绝：处理状态为拒绝
                        wrapper.eq(Appointment::getHandleStatus, HandleEnum.REJECT);
                    }
                }

                appointmentList = this.list(wrapper);
            } else {
                // 没有查询条件，导出全部数据
                LambdaQueryWrapper<Appointment> wrapper = new LambdaQueryWrapper<Appointment>()
                        .orderByDesc(Appointment::getCreateTime);

                appointmentList = this.list(wrapper);
            }
        }

        // 根据MemberId查询用户信息并设置MemberName和Department
        setMemberInfo(appointmentList);

        // 实体转换
        List<AppointmentVO> result = appointmentList.stream()
                .map(entity -> {
                    AppointmentVO vo = new AppointmentVO();
                    BeanUtils.copyProperties(entity, vo);
                    return vo;
                }).collect(Collectors.toList());

        // 设置约见状态
        converterToList(result);

        return result;
    }

    private static void converterToList(List<AppointmentVO> result) {
        for (AppointmentVO vo : result) {
            if (vo.getHandleStatus() == null) {
                vo.setAppointmentStatus(AppoinitMentEnum.WAIT);
            }

            // 如果处理状态为通过，当前时间在约见开始时间之前，设置状态为待开始
            if (vo.getHandleStatus() != null && vo.getHandleStatus().equals(HandleEnum.PASS) && LocalDateTime.now().isBefore(vo.getAppointmentStartTime())) {
                vo.setAppointmentStatus(AppoinitMentEnum.WAIT_START);
            }

            // 如果在约见开始时间和约见结束时间的范围内，设置约见状态为约见中
            if (vo.getAppointmentStartTime() != null && vo.getHandleStatus() != null &&  vo.getHandleStatus().equals(HandleEnum.PASS) && LocalDateTime.now().isAfter(vo.getAppointmentStartTime())
                    && LocalDateTime.now().isBefore(vo.getAppointmentEndTime())) {
                vo.setAppointmentStatus(AppoinitMentEnum.MEETING);
            }

            // 如果约见结束时间小于当前时间，设置约见状态为已结束
            if (vo.getAppointmentEndTime() != null && vo.getHandleStatus() != null && vo.getHandleStatus().equals(HandleEnum.PASS) && vo.getAppointmentEndTime().isBefore(LocalDateTime.now())) {
                vo.setAppointmentStatus(AppoinitMentEnum.END);
            }

            // 如果处理状态为拒绝，设置约见状态为不予约见
            if (vo.getHandleStatus() != null && vo.getHandleStatus().equals(HandleEnum.REJECT)) {
                vo.setAppointmentStatus(AppoinitMentEnum.REJECT);
            }
        }
    }

    /**
     * 根据MemberId查询用户信息并设置MemberName和Department
     * 无论实体中是否已经有值，都会使用从用户表中查询到的最新信息
     *
     * @param voList 约见VO列表
     */
    private void setMemberInfo(List<Appointment> voList) {
        if (voList == null || voList.isEmpty()) {
            return;
        }

        // 遍历每个VO
        for (Appointment vo : voList) {
            try {
                // 直接使用memberId
                Long memberId = null;
                if (vo.getMemberId() != null) {
                    memberId = (vo.getMemberId());
                }

                // 如果memberId为空，则不进行查询，直接使用数据库中已存储的值
                if (memberId == null) {
                    continue; // 跳过当前循环，不进行用户信息的查询
                }

                // 查询用户信息
                UserBO userBO = userMapper.getUserProfile(memberId);

                if (userBO != null) {
                    // 强制覆盖实体中的值，使用从用户表中查询到的最新信息
                    vo.setAppointmentName(userBO.getNickname()); // 使用昵称作为会员名称
                    vo.setAppointmentUnit(userBO.getCompany()); // 使用企业单位名称作为部门
                }
            } catch (Exception e) {
                // 忽略异常
            }
        }
    }


    @Override
    public List<AppointmentVO> getAppointmentListByStatus(AppoinitMentEnum status) {
        LocalDateTime now = LocalDateTime.now();
        
        // 构建查询条件
        LambdaQueryWrapper<Appointment> queryWrapper = new LambdaQueryWrapper<Appointment>();
        
        // 根据约见状态进行过滤
        if (status != null) {
            if (status == AppoinitMentEnum.WAIT) {
                // 待处理：处理状态为空
                queryWrapper.isNull(Appointment::getHandleStatus);
            } else if (status == AppoinitMentEnum.WAIT_START) {
                // 待开始：处理状态为通过，当前时间在开始时间之前
                queryWrapper.eq(Appointment::getHandleStatus, HandleEnum.PASS)
                          .gt(Appointment::getAppointmentStartTime, now);
            } else if (status == AppoinitMentEnum.MEETING) {
                // 进行中：处理状态为通过，当前时间在开始时间之后且在结束时间之前
                queryWrapper.eq(Appointment::getHandleStatus, HandleEnum.PASS)
                          .le(Appointment::getAppointmentStartTime, now)
                          .ge(Appointment::getAppointmentEndTime, now);
            } else if (status == AppoinitMentEnum.END) {
                // 已结束：处理状态为通过，当前时间在结束时间之后
                queryWrapper.eq(Appointment::getHandleStatus, HandleEnum.PASS)
                          .lt(Appointment::getAppointmentEndTime, now);
            } else if (status == AppoinitMentEnum.REJECT) {
                // 已拒绝：处理状态为拒绝
                queryWrapper.eq(Appointment::getHandleStatus, HandleEnum.REJECT);
            }
        }
        
        // 按创建时间倒序排序
        queryWrapper.orderByDesc(Appointment::getCreateTime);
        
        // 查询数据
        List<Appointment> appointmentList = this.list(queryWrapper);
        
        // 根据MemberId查询用户信息并设置MemberName和Department
        setMemberInfo(appointmentList);
        
        // 转换为VO
        return appointmentList.stream()
                .map(entity -> {
                    AppointmentVO vo = new AppointmentVO();
                    vo.setId(entity.getId());
                    vo.setAppointmentName(entity.getAppointmentName());
                    vo.setAppointmentUnit(entity.getAppointmentUnit());
                    vo.setAppointmentContact(entity.getAppointmentContact());
                    vo.setAppointmentDepartment(entity.getAppointmentDepartment());
                    vo.setAppointmentReason(entity.getAppointmentReason());
                    vo.setAppointmentStartTime(entity.getAppointmentStartTime());
                    vo.setAppointmentEndTime(entity.getAppointmentEndTime());
                    vo.setHandleStatus(entity.getHandleStatus());
                    vo.setHandleComment(entity.getHandleComment());
                    vo.setFeedback(entity.getFeedback());
                    vo.setCreateTime(entity.getCreateTime());
                    vo.setAppointmentStatus(status);
                    return vo;
                })
                .collect(Collectors.toList());
    }
}